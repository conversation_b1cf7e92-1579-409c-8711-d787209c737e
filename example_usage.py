#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GLM CrewAI 问答助手使用示例
演示如何使用问答助手进行单次问答和批量问答
"""

from qa_assistant import GLMQAAssistant

def single_question_example():
    """单次问答示例"""
    print("=" * 60)
    print("📝 单次问答示例")
    print("=" * 60)
    
    try:
        # 创建助手实例
        assistant = GLMQAAssistant()
        
        # 示例问题
        question = "什么是人工智能？请简要介绍一下。"
        print(f"问题: {question}")
        print("-" * 60)
        
        # 获取回答
        answer = assistant.ask_question(question)
        print(f"回答: {answer}")
        
    except Exception as e:
        print(f"❌ 错误: {str(e)}")

def batch_questions_example():
    """批量问答示例"""
    print("\n" + "=" * 60)
    print("📋 批量问答示例")
    print("=" * 60)
    
    # 示例问题列表
    questions = [
        "Python是什么编程语言？",
        "机器学习和深度学习有什么区别？",
        "如何学习编程？给一些建议。",
        "什么是区块链技术？"
    ]
    
    try:
        # 创建助手实例
        assistant = GLMQAAssistant()
        
        for i, question in enumerate(questions, 1):
            print(f"\n问题 {i}: {question}")
            print("-" * 40)
            
            # 获取回答
            answer = assistant.ask_question(question)
            print(f"回答: {answer}")
            print("=" * 60)
            
    except Exception as e:
        print(f"❌ 错误: {str(e)}")

def technical_questions_example():
    """技术问题示例"""
    print("\n" + "=" * 60)
    print("🔧 技术问题示例")
    print("=" * 60)
    
    technical_questions = [
        "解释一下什么是RESTful API",
        "Docker和虚拟机有什么区别？",
        "什么是微服务架构？它有什么优缺点？"
    ]
    
    try:
        assistant = GLMQAAssistant()
        
        for i, question in enumerate(technical_questions, 1):
            print(f"\n技术问题 {i}: {question}")
            print("-" * 40)
            
            answer = assistant.ask_question(question)
            print(f"回答: {answer}")
            print("=" * 60)
            
    except Exception as e:
        print(f"❌ 错误: {str(e)}")

def main():
    """主函数 - 运行所有示例"""
    print("🚀 GLM CrewAI 问答助手示例程序")
    print("本程序将演示不同类型的问答场景")
    
    # 运行单次问答示例
    single_question_example()
    
    # 运行批量问答示例
    batch_questions_example()
    
    # 运行技术问题示例
    technical_questions_example()
    
    print("\n" + "=" * 60)
    print("✅ 所有示例运行完成！")
    print("💡 提示: 运行 'python qa_assistant.py' 可以启动交互式问答模式")
    print("=" * 60)

if __name__ == "__main__":
    main()
