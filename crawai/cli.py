"""
命令行界面模块
提供用户友好的交互界面
"""
import sys
import os
from typing import Optional
from .assistant import CrawAIAssistant
from .config import Config


class CrawAICLI:
    """命令行界面类"""
    
    def __init__(self):
        """初始化CLI"""
        self.assistant = None
        self.running = False
        
    def print_banner(self):
        """打印欢迎横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                        CrawAI 问答助手                        ║
║                   基于GLM-4的智能对话系统                     ║
║                                                              ║
║  输入 'help' 查看帮助信息                                     ║
║  输入 'quit' 或 'exit' 退出程序                              ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def print_help(self):
        """打印帮助信息"""
        help_text = """
可用命令：
  help          - 显示此帮助信息
  clear         - 清除对话历史
  history       - 显示对话历史
  info          - 显示会话信息
  export        - 导出对话历史到文件
  quit/exit     - 退出程序
  
直接输入问题即可开始对话。
        """
        print(help_text)
    
    def print_session_info(self):
        """打印会话信息"""
        if not self.assistant:
            print("❌ 助手未初始化")
            return
            
        info = self.assistant.get_session_info()
        print(f"""
📊 会话信息：
  模型: {info['model']}
  温度参数: {info['temperature']}
  会话开始时间: {info['session_start']}
  已提问次数: {info['questions_asked']}
  会话时长: {info['session_duration_seconds']:.1f} 秒
        """)
    
    def print_history(self):
        """打印对话历史"""
        if not self.assistant:
            print("❌ 助手未初始化")
            return
            
        history = self.assistant.get_conversation_history()
        if not history:
            print("📝 暂无对话历史")
            return
        
        print("\n📝 对话历史：")
        print("=" * 60)
        for i, item in enumerate(history, 1):
            print(f"\n[{i}] 问题: {item['question']}")
            print(f"    回答: {item['answer']}")
        print("=" * 60)
    
    def export_conversation(self):
        """导出对话历史"""
        if not self.assistant:
            print("❌ 助手未初始化")
            return
            
        try:
            filename = self.assistant.export_conversation()
            print(f"✅ 对话历史已导出到: {filename}")
        except Exception as e:
            print(f"❌ 导出失败: {str(e)}")
    
    def clear_conversation(self):
        """清除对话历史"""
        if not self.assistant:
            print("❌ 助手未初始化")
            return
            
        self.assistant.clear_conversation()
        print("✅ 对话历史已清除")
    
    def handle_command(self, user_input: str) -> bool:
        """
        处理用户命令
        
        Args:
            user_input (str): 用户输入
            
        Returns:
            bool: 是否继续运行
        """
        command = user_input.strip().lower()
        
        if command in ['quit', 'exit', 'q']:
            return False
        elif command == 'help':
            self.print_help()
        elif command == 'clear':
            self.clear_conversation()
        elif command == 'history':
            self.print_history()
        elif command == 'info':
            self.print_session_info()
        elif command == 'export':
            self.export_conversation()
        else:
            # 普通问题
            self.handle_question(user_input)
        
        return True
    
    def handle_question(self, question: str):
        """
        处理用户问题
        
        Args:
            question (str): 用户问题
        """
        if not self.assistant:
            print("❌ 助手未初始化")
            return
        
        print("🤔 思考中...")
        
        try:
            result = self.assistant.ask(question)
            print(f"\n🤖 CrawAI: {result['answer']}\n")
        except Exception as e:
            print(f"❌ 处理问题时出错: {str(e)}\n")
    
    def run(self):
        """运行CLI主循环"""
        try:
            # 初始化助手
            self.assistant = CrawAIAssistant()
            self.running = True
            
            # 打印欢迎信息
            self.print_banner()
            
            # 主循环
            while self.running:
                try:
                    user_input = input("👤 您: ").strip()
                    
                    if not user_input:
                        continue
                    
                    # 处理命令或问题
                    self.running = self.handle_command(user_input)
                    
                except KeyboardInterrupt:
                    print("\n\n👋 再见！")
                    break
                except EOFError:
                    print("\n\n👋 再见！")
                    break
        
        except Exception as e:
            print(f"❌ 启动失败: {str(e)}")
            print("请检查配置和网络连接。")
            sys.exit(1)


def main():
    """主函数"""
    cli = CrawAICLI()
    cli.run()


if __name__ == "__main__":
    main()
