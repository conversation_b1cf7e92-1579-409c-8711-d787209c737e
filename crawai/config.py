"""
配置管理模块
负责加载和管理应用配置
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """应用配置类"""
    
    # GLM API配置
    ZHIPUAI_API_KEY = os.getenv("ZHIPUAI_API_KEY")
    GLM_MODEL = "glm-4"
    GLM_API_BASE = "https://open.bigmodel.cn/api/paas/v4/"
    
    # 模型参数配置
    TEMPERATURE = 0.95
    MAX_TOKENS = 2048
    
    # 时区配置
    TIMEZONE = "Asia/Shanghai"
    
    @classmethod
    def validate(cls):
        """验证配置是否完整"""
        if not cls.ZHIPUAI_API_KEY:
            raise ValueError("ZHIPUAI_API_KEY 环境变量未设置")
        return True
