"""
问答助手核心模块
提供高级的问答功能和会话管理
"""
import json
import datetime
from typing import Dict, List, Optional
from .llm import GLMChat
from .config import Config


class CrawAIAssistant:
    """crawai问答助手主类"""
    
    def __init__(self):
        """初始化问答助手"""
        self.glm_chat = GLMChat()
        self.session_start_time = datetime.datetime.now()
        self.question_count = 0
        
    def ask(self, question: str) -> Dict[str, any]:
        """
        向助手提问
        
        Args:
            question (str): 用户问题
            
        Returns:
            Dict: 包含回答和元信息的字典
        """
        if not question.strip():
            return {
                "answer": "请输入您的问题。",
                "question": question,
                "timestamp": datetime.datetime.now().isoformat(),
                "question_number": self.question_count
            }
        
        self.question_count += 1
        
        # 获取GLM回答
        answer = self.glm_chat.chat(question)
        
        return {
            "answer": answer,
            "question": question,
            "timestamp": datetime.datetime.now().isoformat(),
            "question_number": self.question_count
        }
    
    def get_session_info(self) -> Dict[str, any]:
        """
        获取当前会话信息
        
        Returns:
            Dict: 会话信息
        """
        current_time = datetime.datetime.now()
        session_duration = current_time - self.session_start_time
        
        return {
            "session_start": self.session_start_time.isoformat(),
            "current_time": current_time.isoformat(),
            "session_duration_seconds": session_duration.total_seconds(),
            "questions_asked": self.question_count,
            "model": Config.GLM_MODEL,
            "temperature": Config.TEMPERATURE
        }
    
    def clear_conversation(self):
        """清除对话历史"""
        self.glm_chat.clear_history()
        self.question_count = 0
        self.session_start_time = datetime.datetime.now()
    
    def get_conversation_history(self) -> List[Dict[str, str]]:
        """
        获取格式化的对话历史
        
        Returns:
            List[Dict]: 对话历史列表
        """
        messages = self.glm_chat.get_history()
        history = []
        
        for i in range(0, len(messages), 2):
            if i + 1 < len(messages):
                history.append({
                    "question": messages[i].content,
                    "answer": messages[i + 1].content,
                    "timestamp": datetime.datetime.now().isoformat()
                })
        
        return history
    
    def export_conversation(self, filename: Optional[str] = None) -> str:
        """
        导出对话历史到JSON文件
        
        Args:
            filename (str, optional): 文件名，如果不提供则自动生成
            
        Returns:
            str: 导出的文件路径
        """
        if not filename:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"crawai_conversation_{timestamp}.json"
        
        export_data = {
            "session_info": self.get_session_info(),
            "conversation_history": self.get_conversation_history()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        return filename
