"""
GLM大语言模型封装模块
基于Lang<PERSON>hain ChatOpenAI实现GLM-4模型调用
"""
from langchain_openai import ChatOpenAI
from langchain.prompts import (
    ChatPromptTemplate,
    MessagesPlaceholder,
    SystemMessagePromptTemplate,
    HumanMessagePromptTemplate,
)
from langchain.chains import LLMChain
from langchain.memory import ConversationBufferMemory
from .config import Config


class GLMChat:
    """GLM聊天模型封装类"""
    
    def __init__(self, temperature=None, model=None):
        """
        初始化GLM聊天模型
        
        Args:
            temperature (float): 温度参数，控制回答的随机性
            model (str): 模型名称
        """
        # 验证配置
        Config.validate()
        
        # 初始化模型参数
        self.temperature = temperature or Config.TEMPERATURE
        self.model = model or Config.GLM_MODEL
        
        # 创建LLM实例
        self.llm = ChatOpenAI(
            temperature=self.temperature,
            model=self.model,
            openai_api_key=Config.ZHIPUAI_API_KEY,
            openai_api_base=Config.GLM_API_BASE,
            max_tokens=Config.MAX_TOKENS
        )
        
        # 创建对话提示模板
        self.prompt = ChatPromptTemplate(
            messages=[
                SystemMessagePromptTemplate.from_template(
                    "你是一个友好、专业的AI助手。请用中文回答用户的问题，"
                    "提供准确、有用的信息。如果不确定答案，请诚实地说明。"
                ),
                MessagesPlaceholder(variable_name="chat_history"),
                HumanMessagePromptTemplate.from_template("{question}"),
            ]
        )
        
        # 创建对话记忆
        self.memory = ConversationBufferMemory(
            memory_key="chat_history", 
            return_messages=True
        )
        
        # 创建对话链
        self.conversation = LLMChain(
            llm=self.llm,
            prompt=self.prompt,
            verbose=False,
            memory=self.memory
        )
    
    def chat(self, question: str) -> str:
        """
        与GLM进行对话
        
        Args:
            question (str): 用户问题
            
        Returns:
            str: GLM的回答
        """
        try:
            response = self.conversation.invoke({"question": question})
            return response["text"]
        except Exception as e:
            return f"抱歉，处理您的问题时出现错误：{str(e)}"
    
    def clear_history(self):
        """清除对话历史"""
        self.memory.clear()
    
    def get_history(self) -> list:
        """
        获取对话历史
        
        Returns:
            list: 对话历史消息列表
        """
        return self.memory.chat_memory.messages
