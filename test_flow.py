#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CrewAI Flows测试脚本
快速验证工作流功能
"""

from coser_analysis_flow import run_coser_analysis

def test_flow():
    """测试CrewAI Flows工作流"""
    print("🧪 CrewAI Flows功能测试")
    print("=" * 50)
    
    # 测试参数
    image_path = "static/WechatIMG8_640xNaN.jpg"
    character_name = "蓝发战士"
    additional_description = "一个拥有蓝色长发的神秘战士角色，穿着黑色战斗服装"
    
    print(f"📸 测试图片: {image_path}")
    print(f"🎭 角色名称: {character_name}")
    print(f"📝 补充描述: {additional_description}")
    print("-" * 50)
    
    try:
        # 运行工作流
        result = run_coser_analysis(image_path, character_name, additional_description)
        
        if result:
            print("\n✅ 工作流测试成功！")
            print("\n📊 测试结果摘要:")
            print(f"• 图片分析长度: {len(result['image_analysis'])} 字符")
            print(f"• 综合分析长度: {len(result['comprehensive_analysis'])} 字符")
            print(f"• 角色名称: {result['character_name']}")
            
            # 显示部分结果
            print("\n🖼️  图片分析预览:")
            preview = result['image_analysis'][:200] + "..." if len(result['image_analysis']) > 200 else result['image_analysis']
            print(preview)
            
            print("\n🔍 综合分析预览:")
            preview = result['comprehensive_analysis'][:200] + "..." if len(result['comprehensive_analysis']) > 200 else result['comprehensive_analysis']
            print(preview)
            
            return True
        else:
            print("\n❌ 工作流测试失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试异常: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_flow()
    if success:
        print("\n🎉 CrewAI Flows工作流运行正常！")
    else:
        print("\n💔 工作流存在问题，请检查配置")
