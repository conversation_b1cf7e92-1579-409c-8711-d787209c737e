#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式测试问答助手
"""

from qa_assistant import GLMQAAssistant

def main():
    """交互式测试"""
    print("🚀 GLM CrewAI 问答助手 - 交互式测试")
    print("=" * 60)
    
    try:
        # 创建助手实例
        assistant = GLMQAAssistant()
        print("✅ 助手初始化成功")
        
        # 预设几个测试问题
        test_questions = [
            "什么是人工智能？",
            "Python有什么优势？",
            "解释一下什么是机器学习"
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n📝 测试问题 {i}: {question}")
            print("-" * 50)
            
            try:
                answer = assistant.ask_question(question)
                print(f"🤖 回答: {answer}")
                print("=" * 60)
            except Exception as e:
                print(f"❌ 回答失败: {str(e)}")
        
        print("\n✅ 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")

if __name__ == "__main__":
    main()
