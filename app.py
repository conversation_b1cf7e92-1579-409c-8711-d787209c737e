#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Coser分析工作流 Web应用
基于Flask的前端界面
"""

import os
import json
import uuid
from datetime import datetime
from flask import Flask, render_template, request, jsonify, send_from_directory
from werkzeug.utils import secure_filename
from coser_analysis_flow import run_coser_analysis

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 配置上传目录
UPLOAD_FOLDER = 'static/uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}

# 确保上传目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """处理文件上传"""
    try:
        print(f"📤 收到上传请求")
        print(f"📋 请求文件: {list(request.files.keys())}")

        if 'file' not in request.files:
            print("❌ 错误: 没有找到文件字段")
            return jsonify({'error': '没有选择文件'}), 400

        file = request.files['file']
        print(f"📁 文件名: {file.filename}")
        print(f"📊 文件类型: {file.content_type}")

        if file.filename == '':
            print("❌ 错误: 文件名为空")
            return jsonify({'error': '没有选择文件'}), 400

        if file and allowed_file(file.filename):
            # 生成安全的文件名
            filename = secure_filename(file.filename)
            unique_filename = f"{uuid.uuid4().hex}_{filename}"
            filepath = os.path.join(UPLOAD_FOLDER, unique_filename)

            print(f"💾 保存文件到: {filepath}")

            # 保存文件
            file.save(filepath)

            print(f"✅ 文件上传成功: {unique_filename}")

            return jsonify({
                'success': True,
                'filename': unique_filename,
                'filepath': filepath
            })
        else:
            print(f"❌ 错误: 不支持的文件格式 - {file.filename}")
            return jsonify({'error': '不支持的文件格式'}), 400

    except Exception as e:
        print(f"💥 上传异常: {str(e)}")
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

@app.route('/analyze', methods=['POST'])
def analyze_image():
    """分析图片"""
    try:
        data = request.get_json()
        
        # 获取参数
        filepath = data.get('filepath')
        character_name = data.get('character_name', '')
        additional_description = data.get('additional_description', '')
        
        if not filepath or not os.path.exists(filepath):
            return jsonify({'error': '图片文件不存在'}), 400
        
        if not character_name.strip():
            return jsonify({'error': '请输入角色名称'}), 400
        
        # 调用分析功能
        result = run_coser_analysis(
            image_path=filepath,
            character_name=character_name.strip(),
            additional_description=additional_description.strip()
        )
        
        # 保存分析结果
        result_data = {
            'timestamp': datetime.now().isoformat(),
            'character_name': character_name,
            'additional_description': additional_description,
            'image_analysis': result['image_analysis'],
            'comprehensive_analysis': result['comprehensive_analysis']
        }
        
        return jsonify({
            'success': True,
            'result': result_data
        })
        
    except Exception as e:
        return jsonify({'error': f'分析失败: {str(e)}'}), 500

@app.route('/download_result', methods=['POST'])
def download_result():
    """下载分析结果"""
    try:
        data = request.get_json()
        result_data = data.get('result')
        
        if not result_data:
            return jsonify({'error': '没有分析结果'}), 400
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        character_name = result_data.get('character_name', 'unknown')
        filename = f"coser_analysis_{character_name}_{timestamp}.txt"
        
        # 格式化内容
        content = f"""Coser分析结果
================

分析时间: {result_data.get('timestamp', '')}
角色名称: {result_data.get('character_name', '')}
补充描述: {result_data.get('additional_description', '')}

图片分析结果:
{'-' * 50}
{result_data.get('image_analysis', '')}

综合分析结果:
{'-' * 50}
{result_data.get('comprehensive_analysis', '')}
"""
        
        # 保存到临时文件
        temp_filepath = os.path.join(UPLOAD_FOLDER, filename)
        with open(temp_filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return jsonify({
            'success': True,
            'download_url': f'/download/{filename}'
        })
        
    except Exception as e:
        return jsonify({'error': f'生成下载文件失败: {str(e)}'}), 500

@app.route('/download/<filename>')
def download_file(filename):
    """下载文件"""
    try:
        return send_from_directory(UPLOAD_FOLDER, filename, as_attachment=True)
    except Exception as e:
        return jsonify({'error': f'下载失败: {str(e)}'}), 500

@app.errorhandler(413)
def too_large(e):
    """文件过大错误处理"""
    return jsonify({'error': '文件大小超过限制（最大16MB）'}), 413

@app.errorhandler(404)
def not_found(e):
    """404错误处理"""
    return jsonify({'error': '页面不存在'}), 404

@app.errorhandler(500)
def internal_error(e):
    """500错误处理"""
    return jsonify({'error': '服务器内部错误'}), 500

if __name__ == '__main__':
    print("🚀 启动Coser分析工作流Web应用")
    print("📱 访问地址: http://localhost:8888")
    print("🎭 开始您的Coser分析之旅！")

    # 开发模式运行
    app.run(
        host='0.0.0.0',
        port=8888,
        debug=True,
        threaded=True
    )
