#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GLM CrewAI 问答助手
使用智谱AI的GLM模型和CrewAI框架构建的智能问答系统
"""

import os
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process
from langchain_openai import ChatOpenAI

# 加载环境变量
load_dotenv()

class GLMQAAssistant:
    """GLM问答助手类"""
    
    def __init__(self):
        """初始化GLM问答助手"""
        self.llm = self._setup_llm()
        self.agent = self._create_agent()
        
    def _setup_llm(self):
        """设置GLM大语言模型"""
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            raise ValueError("请在.env文件中设置ZHIPUAI_API_KEY")
            
        return ChatOpenAI(
            temperature=0.7,
            model="glm-4",
            openai_api_key=api_key,
            openai_api_base="https://open.bigmodel.cn/api/paas/v4/",
            max_tokens=2000
        )
    
    def _create_agent(self):
        """创建问答代理"""
        return Agent(
            role='智能问答助手',
            goal='为用户提供准确、有用的回答',
            backstory="""
            你是一个专业的智能问答助手，具备广泛的知识储备。
            你能够理解用户的问题，并提供清晰、准确、有帮助的回答。
            你会根据问题的复杂程度调整回答的详细程度，
            对于复杂问题会提供结构化的解答。
            """,
            verbose=True,
            allow_delegation=False,
            llm=self.llm
        )
    
    def ask_question(self, question: str) -> str:
        """
        处理用户问题并返回回答

        Args:
            question (str): 用户提出的问题

        Returns:
            str: 助手的回答
        """
        try:
            # 创建任务
            task = Task(
                description=f"""
                请回答以下问题：{question}

                要求：
                1. 回答要准确、有用
                2. 如果问题复杂，请提供结构化的回答
                3. 如果需要，可以提供相关的背景信息
                4. 保持回答的简洁性，避免冗余
                5. 使用中文回答
                """,
                expected_output="对用户问题的详细、准确回答",
                agent=self.agent
            )

            # 创建团队并执行任务
            crew = Crew(
                agents=[self.agent],
                tasks=[task],
                verbose=False,  # 关闭详细输出以减少干扰
                process=Process.sequential
            )

            result = crew.kickoff()

            # 处理返回结果
            if hasattr(result, 'raw'):
                return result.raw
            elif hasattr(result, 'output'):
                return result.output
            else:
                return str(result)

        except Exception as e:
            # 如果CrewAI失败，直接使用LLM
            print(f"⚠️  CrewAI执行失败，使用直接LLM调用: {str(e)}")
            return self._direct_llm_call(question)

    def _direct_llm_call(self, question: str) -> str:
        """
        直接调用LLM进行问答（备用方案）

        Args:
            question (str): 用户问题

        Returns:
            str: LLM回答
        """
        try:
            prompt = f"""
            请回答以下问题：{question}

            要求：
            1. 回答要准确、有用
            2. 如果问题复杂，请提供结构化的回答
            3. 如果需要，可以提供相关的背景信息
            4. 保持回答的简洁性，避免冗余
            5. 使用中文回答
            """

            response = self.llm.invoke(prompt)
            return response.content

        except Exception as e:
            return f"❌ 抱歉，处理您的问题时出现错误: {str(e)}"
    
    def interactive_chat(self):
        """启动交互式聊天模式"""
        print("=" * 60)
        print("🤖 GLM CrewAI 问答助手")
        print("=" * 60)
        print("欢迎使用智能问答助手！输入 'quit' 或 'exit' 退出程序")
        print("-" * 60)
        
        while True:
            try:
                # 获取用户输入
                question = input("\n💬 请输入您的问题: ").strip()
                
                # 检查退出命令
                if question.lower() in ['quit', 'exit', '退出', 'q']:
                    print("\n👋 感谢使用，再见！")
                    break
                
                # 检查空输入
                if not question:
                    print("❌ 请输入有效的问题")
                    continue
                
                print(f"\n🤔 正在思考您的问题: {question}")
                print("-" * 60)
                
                # 获取回答
                answer = self.ask_question(question)
                
                print(f"\n🤖 助手回答:")
                print(f"{answer}")
                print("-" * 60)
                
            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，再见！")
                break
            except Exception as e:
                print(f"\n❌ 发生错误: {str(e)}")
                print("请重试或检查网络连接")


def main():
    """主函数"""
    try:
        # 创建问答助手实例
        assistant = GLMQAAssistant()
        
        # 启动交互式聊天
        assistant.interactive_chat()
        
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")
        print("请检查：")
        print("1. .env文件中是否正确设置了ZHIPUAI_API_KEY")
        print("2. 网络连接是否正常")
        print("3. API密钥是否有效")


if __name__ == "__main__":
    main()
