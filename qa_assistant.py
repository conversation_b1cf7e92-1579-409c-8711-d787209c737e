#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GLM CrewAI 问答助手
使用智谱AI的GLM模型和CrewAI框架构建的智能问答系统
"""

import os
import base64
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process
from langchain_openai import ChatOpenAI

# 加载环境变量
load_dotenv()

class GLMQAAssistant:
    """GLM问答助手类"""
    
    def __init__(self):
        """初始化GLM问答助手"""
        self.llm = self._setup_llm()
        self.llm_vision = self._setup_vision_llm()
        self.agent = self._create_agent()
        self.vision_agent = self._create_vision_agent()
        
    def _setup_llm(self):
        """设置GLM大语言模型"""
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            raise ValueError("请在.env文件中设置ZHIPUAI_API_KEY")

        return ChatOpenAI(
            temperature=0.7,
            model="glm-4",
            openai_api_key=api_key,
            openai_api_base="https://open.bigmodel.cn/api/paas/v4/",
            max_tokens=2000
        )

    def _setup_vision_llm(self):
        """设置GLM-4V视觉模型"""
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            raise ValueError("请在.env文件中设置ZHIPUAI_API_KEY")

        return ChatOpenAI(
            temperature=0.7,
            model="glm-4v",
            openai_api_key=api_key,
            openai_api_base="https://open.bigmodel.cn/api/paas/v4/",
            max_tokens=2000
        )
    
    def _create_agent(self):
        """创建问答代理"""
        return Agent(
            role='智能问答助手',
            goal='为用户提供准确、有用的回答',
            backstory="""
            你是一个专业的智能问答助手，具备广泛的知识储备。
            你能够理解用户的问题，并提供清晰、准确、有帮助的回答。
            你会根据问题的复杂程度调整回答的详细程度，
            对于复杂问题会提供结构化的解答。
            """,
            verbose=True,
            allow_delegation=False,
            llm=self.llm
        )

    def _create_vision_agent(self):
        """创建图片分析代理"""
        return Agent(
            role='Coser图片分析专家',
            goal='专业分析Coser图片的各个方面，提供详细的视觉分析报告',
            backstory="""
            你是一个专业的Coser图片分析专家，具备丰富的二次元文化知识和视觉分析能力。
            你能够准确识别和分析Coser图片中的各种元素，包括动作姿态、武器道具、
            服饰细节、发型发色、妆容分析和光线氛围等方面。
            你的分析准确、专业，能够为用户提供有价值的视觉分析报告。
            """,
            verbose=False,
            allow_delegation=False,
            llm=self.llm_vision
        )
    
    def ask_question(self, question: str) -> str:
        """
        处理用户问题并返回回答

        Args:
            question (str): 用户提出的问题

        Returns:
            str: 助手的回答
        """
        # 直接使用LLM调用，避免CrewAI的不稳定性和错误提示
        return self._direct_llm_call(question)

    def _direct_llm_call(self, question: str) -> str:
        """
        直接调用LLM进行问答（备用方案）

        Args:
            question (str): 用户问题

        Returns:
            str: LLM回答
        """
        try:
            prompt = f"""
            请回答以下问题：{question}

            要求：
            1. 回答要准确、有用
            2. 如果问题复杂，请提供结构化的回答
            3. 如果需要，可以提供相关的背景信息
            4. 保持回答的简洁性，避免冗余
            5. 使用中文回答
            """

            response = self.llm.invoke(prompt)
            return response.content

        except Exception as e:
            return f"❌ 抱歉，处理您的问题时出现错误: {str(e)}"

    def _encode_image_to_base64(self, image_path: str) -> str:
        """
        将图片文件编码为base64格式

        Args:
            image_path (str): 图片文件路径

        Returns:
            str: base64编码的图片数据
        """
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            raise ValueError(f"无法读取图片文件: {str(e)}")

    def analyze_coser_image(self, image_path: str) -> str:
        """
        分析Coser图片的各个方面

        Args:
            image_path (str): 图片文件路径

        Returns:
            str: 详细的图片分析结果
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                return f"❌ 错误: 图片文件不存在: {image_path}"

            # 编码图片为base64
            base64_image = self._encode_image_to_base64(image_path)

            # 创建分析任务
            task = Task(
                description=f"""
                请分析这张Coser图片，从以下几个方面进行详细分析：

                1. **动作姿态**：识别coser的具体动作（如"跳舞"、"战斗姿势"、"胜利手势"、"微笑挥手"等）
                2. **武器道具**：识别手持或佩戴的道具（如"拿着剑"、"手持法杖"、"背着弓箭"、"戴着头饰"等）
                3. **服饰细节**：分析服装特征（如"穿着校服"、"战斗装"、"和服"、"哥特洛丽塔"等）
                4. **发型发色**：识别coser的发型和发色特征（如"长发"、"短发"、"金色长发"、"黄色长发"等）
                5. **妆容分析**：观察化妆风格（如"淡妆"、"华丽妆容"、"舞台妆"、"特效妆"等）
                6. **光线氛围**：分析打光效果（如"柔光"、"戏剧性光影"、"彩色灯光"等）

                请按照上述分类，逐一进行详细分析，并提供专业的观察结果。
                使用中文回答，格式要清晰易读。

                图片数据: data:image/jpeg;base64,{base64_image}
                """,
                expected_output="对Coser图片的专业分析报告，包含所有要求的分析维度",
                agent=self.vision_agent
            )

            # 创建团队并执行任务
            crew = Crew(
                agents=[self.vision_agent],
                tasks=[task],
                verbose=False,
                process=Process.sequential
            )

            result = crew.kickoff()

            # 处理返回结果
            if hasattr(result, 'raw'):
                return result.raw
            elif hasattr(result, 'output'):
                return result.output
            else:
                return str(result)

        except Exception as e:
            # 如果CrewAI失败，使用直接LLM调用
            return self._direct_vision_llm_call(image_path)

    def _direct_vision_llm_call(self, image_path: str) -> str:
        """
        直接调用GLM-4V进行图片分析（备用方案）

        Args:
            image_path (str): 图片文件路径

        Returns:
            str: 图片分析结果
        """
        try:
            # 编码图片为base64
            base64_image = self._encode_image_to_base64(image_path)

            # 构建包含图片的消息
            from langchain_core.messages import HumanMessage

            message = HumanMessage(
                content=[
                    {
                        "type": "text",
                        "text": """请分析这张Coser图片，从以下几个方面进行详细分析：

1. **动作姿态**：识别coser的具体动作（如"跳舞"、"战斗姿势"、"胜利手势"、"微笑挥手"等）
2. **武器道具**：识别手持或佩戴的道具（如"拿着剑"、"手持法杖"、"背着弓箭"、"戴着头饰"等）
3. **服饰细节**：分析服装特征（如"穿着校服"、"战斗装"、"和服"、"哥特洛丽塔"等）
4. **发型发色**：识别coser的发型和发色特征（如"长发"、"短发"、"金色长发"、"黄色长发"等）
5. **妆容分析**：观察化妆风格（如"淡妆"、"华丽妆容"、"舞台妆"、"特效妆"等）
6. **光线氛围**：分析打光效果（如"柔光"、"戏剧性光影"、"彩色灯光"等）

请按照上述分类，逐一进行详细分析，并提供专业的观察结果。使用中文回答，格式要清晰易读。"""
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}"
                        }
                    }
                ]
            )

            response = self.llm_vision.invoke([message])
            return response.content

        except Exception as e:
            return f"❌ 抱歉，分析图片时出现错误: {str(e)}"
    
    def interactive_chat(self):
        """启动交互式聊天模式"""
        print("=" * 60)
        print("🤖 GLM CrewAI 智能助手")
        print("=" * 60)
        print("功能说明：")
        print("💬 文字问答：直接输入问题")
        print("🖼️  图片分析：输入 'image:图片路径' 分析Coser图片")
        print("🚪 退出程序：输入 'quit' 或 'exit'")
        print("-" * 60)
        
        while True:
            try:
                # 获取用户输入
                user_input = input("\n💬 请输入问题或图片路径: ").strip()

                # 检查退出命令
                if user_input.lower() in ['quit', 'exit', '退出', 'q']:
                    print("\n👋 感谢使用，再见！")
                    break

                # 检查空输入
                if not user_input:
                    print("❌ 请输入有效的问题或图片路径")
                    continue

                # 检查是否是图片分析请求
                if user_input.lower().startswith('image:'):
                    image_path = user_input[6:].strip()
                    print(f"\n🖼️  正在分析图片: {image_path}")
                    print("-" * 60)

                    # 分析图片
                    analysis_result = self.analyze_coser_image(image_path)

                    print(f"\n📊 图片分析结果:")
                    print(f"{analysis_result}")
                    print("-" * 60)
                else:
                    # 普通文字问答
                    print(f"\n🤔 正在思考您的问题: {user_input}")
                    print("-" * 60)

                    # 获取回答
                    answer = self.ask_question(user_input)

                    print(f"\n🤖 助手回答:")
                    print(f"{answer}")
                    print("-" * 60)
                
            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，再见！")
                break
            except Exception as e:
                print(f"\n❌ 发生错误: {str(e)}")
                print("请重试或检查网络连接")


def main():
    """主函数"""
    try:
        # 创建问答助手实例
        assistant = GLMQAAssistant()
        
        # 启动交互式聊天
        assistant.interactive_chat()
        
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")
        print("请检查：")
        print("1. .env文件中是否正确设置了ZHIPUAI_API_KEY")
        print("2. 网络连接是否正常")
        print("3. API密钥是否有效")


if __name__ == "__main__":
    main()
