#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Coser分析工作流 - 使用CrewAI Flows
包含图片分析和综合分析两个步骤
"""

import os
import base64
from typing import Dict, Any
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process, LLM
from crewai.flow.flow import Flow, listen, start
from pydantic import BaseModel

# 加载环境变量
load_dotenv()

class CoserAnalysisState(BaseModel):
    """Coser分析状态模型"""
    image_path: str = ""
    character_name: str = ""
    additional_description: str = ""
    image_analysis: str = ""
    comprehensive_analysis: str = ""

class CoserAnalysisFlow(Flow[CoserAnalysisState]):
    """Coser分析工作流"""

    def __init__(self):
        """初始化工作流"""
        super().__init__()

        # 设置GLM模型
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            raise ValueError("请在.env文件中设置ZHIPUAI_API_KEY")

        # GLM-4V视觉模型
        self.vision_llm = LLM(
            model="glm-4v",
            api_key=api_key,
            base_url="https://open.bigmodel.cn/api/paas/v4/",
            temperature=0.7
        )

        # GLM-Z1-Air文本模型
        self.text_llm = LLM(
            model="glm-z1-air",
            api_key=api_key,
            base_url="https://open.bigmodel.cn/api/paas/v4/",
            temperature=0.7
        )

        # 创建代理
        self.image_analysis_agent = self._create_image_analysis_agent()
        self.comprehensive_analysis_agent = self._create_comprehensive_analysis_agent()

    def _create_image_analysis_agent(self):
        """创建图片分析代理"""
        return Agent(
            role='Coser图片分析专家',
            goal='专业分析Coser图片的各个方面，提供详细的视觉分析报告',
            backstory="""
            你是一个专业的Coser图片分析专家，具备丰富的二次元文化知识和视觉分析能力。
            你能够准确识别和分析Coser图片中的各种元素，包括动作姿态、武器道具、
            服饰细节、发型发色、妆容分析和光线氛围等方面。
            你的分析准确、专业，能够为后续的综合分析提供有价值的基础信息。
            """,
            verbose=False,
            allow_delegation=False,
            llm=self.vision_llm
        )

    def _create_comprehensive_analysis_agent(self):
        """创建综合分析代理"""
        return Agent(
            role='二次元角色综合分析专家',
            goal='整合coser场照分析信息和角色设定，生成角色详细信息和文生图prompt',
            backstory="""
            你是一个专业的二次元角色助手，擅长整合coser场照分析信息和角色设定，
            为文生图应用生成高质量的prompt提示词。你具备深厚的二次元文化知识，
            能够准确识别各种角色设定，并结合coser表演信息设计新的背景环境和前景特效。
            你的分析全面、创意丰富，能够生成适合不同需求的文生图prompt方案。
            """,
            verbose=False,
            allow_delegation=False,
            llm=self.text_llm
        )

    def _encode_image_to_base64(self, image_path: str) -> str:
        """将图片文件编码为base64格式"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            raise ValueError(f"无法读取图片文件: {str(e)}")
    
    @start()
    def start_analysis(self):
        """开始分析流程"""
        print(f"🚀 开始Coser分析流程")
        print(f"📸 图片路径: {self.state.image_path}")
        print(f"🎭 角色名称: {self.state.character_name}")
        print(f"📝 补充描述: {self.state.additional_description}")

        return "分析流程已启动"
    
    @listen(start_analysis)
    def image_analysis_step(self, previous_result):
        """步骤一：图片分析"""
        print("\n🖼️  步骤一：图片分析中...")

        try:
            # 检查文件是否存在
            if not os.path.exists(self.state.image_path):
                raise ValueError(f"图片文件不存在: {self.state.image_path}")

            # 编码图片为base64
            base64_image = self._encode_image_to_base64(self.state.image_path)

            # 创建图片分析任务
            task = Task(
                description=f"""
                请分析这张Coser图片，从以下六个方面进行详细分析：

                1. **动作姿态**：识别coser的具体动作（如"跳舞"、"战斗姿势"、"胜利手势"、"微笑挥手"等）
                2. **武器道具**：识别手持或佩戴的道具（如"拿着剑"、"手持法杖"、"背着弓箭"、"戴着头饰"等）
                3. **服饰细节**：分析服装特征（如"穿着校服"、"战斗装"、"和服"、"哥特洛丽塔"等）
                4. **发型发色**：识别coser的发型和发色特征（如"长发"、"短发"、"金色长发"、"蓝色长发"等）
                5. **妆容分析**：观察化妆风格（如"淡妆"、"华丽妆容"、"舞台妆"、"特效妆"等）
                6. **光线氛围**：分析打光效果（如"柔光"、"戏剧性光影"、"彩色灯光"等）

                请按照上述分类，逐一进行详细分析，并提供专业的观察结果。
                使用中文回答，格式要清晰易读。

                图片数据: data:image/jpeg;base64,{base64_image}
                """,
                expected_output="对Coser图片的专业分析报告，包含所有要求的分析维度",
                agent=self.image_analysis_agent
            )

            # 执行任务
            crew = Crew(
                agents=[self.image_analysis_agent],
                tasks=[task],
                verbose=False,
                process=Process.sequential
            )

            result = crew.kickoff()

            # 处理返回结果并存储到状态
            analysis_result = result.raw if hasattr(result, 'raw') else str(result)
            self.state.image_analysis = analysis_result

            print("✅ 图片分析完成")

            return analysis_result

        except Exception as e:
            error_msg = f"图片分析失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.state.image_analysis = error_msg
            return error_msg
    
    @listen(image_analysis_step)
    def comprehensive_analysis_step(self, image_analysis_result):
        """步骤二：综合分析"""
        print("\n🔍 步骤二：综合分析中...")

        try:
            # 读取系统提示文件
            prompt_file_path = "system-prompts/comprehensive-analysis-prompt.md"
            if os.path.exists(prompt_file_path):
                with open(prompt_file_path, 'r', encoding='utf-8') as f:
                    system_prompt = f.read()
            else:
                system_prompt = "请按照专业的二次元角色分析标准进行综合分析。"

            # 创建综合分析任务
            task = Task(
                description=f"""
                {system_prompt}

                请根据以上系统提示，对以下信息进行综合分析：

                **角色名称**: {self.state.character_name}

                **补充描述**: {self.state.additional_description}

                **Coser图片分析结果**:
                {self.state.image_analysis}

                请严格按照系统提示的格式要求，输出以下两部分内容：

                1. **角色的详细信息**（包括场照信息确认、角色信息分析、信息整合分析）
                2. **三组文生图的中英文prompt**（场照致敬、官方还原、创意融合）

                请确保输出格式完整、专业，符合文生图应用的需求。
                """,
                expected_output="完整的角色详细信息和三组文生图prompt方案",
                agent=self.comprehensive_analysis_agent
            )

            # 执行任务
            crew = Crew(
                agents=[self.comprehensive_analysis_agent],
                tasks=[task],
                verbose=False,
                process=Process.sequential
            )

            result = crew.kickoff()

            # 处理返回结果并存储到状态
            comprehensive_result = result.raw if hasattr(result, 'raw') else str(result)
            self.state.comprehensive_analysis = comprehensive_result

            print("✅ 综合分析完成")

            return comprehensive_result

        except Exception as e:
            error_msg = f"综合分析失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.state.comprehensive_analysis = error_msg
            return error_msg


def run_coser_analysis(image_path: str, character_name: str, additional_description: str = ""):
    """运行Coser分析工作流"""
    print("🎭 Coser分析工作流")
    print("=" * 60)

    try:
        # 创建工作流实例
        flow = CoserAnalysisFlow()

        # 设置初始状态
        flow.state.image_path = image_path
        flow.state.character_name = character_name
        flow.state.additional_description = additional_description

        # 运行工作流
        result = flow.kickoff()

        print("\n" + "=" * 60)
        print("🎉 工作流执行完成！")
        print("=" * 60)

        # 输出结果
        print("\n📋 最终分析结果:")
        print(f"图片分析: {flow.state.image_analysis[:200]}..." if len(flow.state.image_analysis) > 200 else flow.state.image_analysis)
        print(f"\n综合分析: {flow.state.comprehensive_analysis[:200]}..." if len(flow.state.comprehensive_analysis) > 200 else flow.state.comprehensive_analysis)

        return {
            "image_analysis": flow.state.image_analysis,
            "comprehensive_analysis": flow.state.comprehensive_analysis,
            "character_name": flow.state.character_name
        }

    except Exception as e:
        print(f"\n❌ 工作流执行失败: {str(e)}")
        return None


def main():
    """主函数 - 演示工作流使用"""
    result = run_coser_analysis(
        image_path="static/WechatIMG8_640xNaN.jpg",
        character_name="测试角色",
        additional_description="这是一个测试用的角色描述"
    )

    if result:
        print("\n✅ 分析完成，结果已保存")
    else:
        print("\n❌ 分析失败")


if __name__ == "__main__":
    main()
