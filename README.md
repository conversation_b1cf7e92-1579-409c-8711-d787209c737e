# GLM CrewAI 问答助手

基于智谱AI的GLM模型和CrewAI框架构建的智能问答系统。

## 功能特点

- 🤖 使用智谱AI GLM-4模型提供智能回答
- 🖼️ 使用GLM-4V模型进行Coser图片分析
- 🔧 基于CrewAI框架构建，支持复杂任务处理
- 💬 支持交互式聊天模式
- 📝 支持单次问答和批量问答
- 🎭 专业的Coser图片分析功能
- 🌍 中文优化，提供准确的中文回答
- ⚙️ 可配置的模型参数
- 🚫 无干扰模式，隐藏技术错误提示
- 🔄 智能备用机制，确保稳定运行

## 环境要求

- Python 3.11+
- 智谱AI API密钥

## 安装依赖

```bash
pip install crewai langchain langchain-openai python-dotenv
```

## 配置

1. 在项目根目录创建 `.env` 文件
2. 添加您的智谱AI API密钥：

```env
ZHIPUAI_API_KEY=your_zhipuai_api_key_here
```

## 版本选择

项目提供两个版本：

1. **qa_assistant.py** (推荐)
   - 无干扰版本，直接使用GLM模型
   - 没有技术错误提示，用户体验更好
   - 响应速度更快，更稳定

2. **qa_assistant_with_crewai.py**
   - 保留CrewAI框架功能
   - 支持更复杂的任务处理
   - 隐藏了错误提示但保留CrewAI能力

## 使用方法

### 1. 交互式模式

运行主程序启动交互式聊天：

```bash
python qa_assistant.py
```

在交互模式下：
- **文字问答**: 直接输入问题
- **图片分析**: 输入 `image:图片路径` 分析Coser图片
- **退出程序**: 输入 `quit` 或 `exit`

或使用CrewAI版本：

```bash
python qa_assistant_with_crewai.py
```

### 2. 编程方式使用

**文字问答**:
```python
from qa_assistant import GLMQAAssistant

# 创建助手实例
assistant = GLMQAAssistant()

# 单次问答
question = "什么是人工智能？"
answer = assistant.ask_question(question)
print(answer)
```

**图片分析**:
```python
from qa_assistant import GLMQAAssistant

# 创建助手实例
assistant = GLMQAAssistant()

# 分析Coser图片
image_path = "coser_photo.jpg"
analysis = assistant.analyze_coser_image(image_path)
print(analysis)
```

### 3. 快速测试

运行测试脚本验证功能：

```bash
python test_image_analysis.py
python image_analysis_example.py
```

## Coser图片分析功能

### 分析维度

系统会从以下六个专业维度分析Coser图片：

1. **动作姿态**: 识别coser的具体动作（如"跳舞"、"战斗姿势"、"胜利手势"、"微笑挥手"等）
2. **武器道具**: 识别手持或佩戴的道具（如"拿着剑"、"手持法杖"、"背着弓箭"、"戴着头饰"等）
3. **服饰细节**: 分析服装特征（如"穿着校服"、"战斗装"、"和服"、"哥特洛丽塔"等）
4. **发型发色**: 识别coser的发型和发色特征（如"长发"、"短发"、"金色长发"、"黄色长发"等）
5. **妆容分析**: 观察化妆风格（如"淡妆"、"华丽妆容"、"舞台妆"、"特效妆"等）
6. **光线氛围**: 分析打光效果（如"柔光"、"戏剧性光影"、"彩色灯光"等）

### 使用示例

**交互式使用**:
```
💬 请输入问题或图片路径: image:my_coser_photo.jpg
🖼️  正在分析图片: my_coser_photo.jpg
📊 图片分析结果:
[详细的六维度分析结果]
```

**支持的图片格式**: jpg, jpeg, png, bmp等常见格式

## 项目结构

```
.
├── qa_assistant.py              # 主要的问答助手类（推荐使用）
├── qa_assistant_with_crewai.py  # 保留CrewAI功能的版本
├── README.md                    # 项目说明
├── 项目总结.md                  # 项目总结报告
└── .env                        # 环境变量配置（需要自己创建）
```

## 示例问题

系统可以回答各种类型的问题：

### 通用知识
- "什么是人工智能？"
- "如何学习编程？"
- "解释一下区块链技术"

### 技术问题
- "什么是RESTful API？"
- "Docker和虚拟机有什么区别？"
- "解释微服务架构的优缺点"

## 注意事项

1. 确保网络连接正常，能够访问智谱AI API
2. API密钥需要有足够的调用额度
3. 首次运行可能需要下载模型，请耐心等待
4. 建议在稳定的网络环境下使用

---

**享受与AI助手的对话吧！** 🚀