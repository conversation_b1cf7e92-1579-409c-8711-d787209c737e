# GLM CrewAI 问答助手

基于智谱AI的GLM模型和CrewAI框架构建的智能问答系统。

## 功能特点

- 🤖 使用智谱AI GLM-Z1-Air模型提供智能文字回答
- 🖼️ 使用GLM-4V模型进行Coser图片分析
- 🔧 基于CrewAI框架构建，支持复杂任务处理
- 💬 支持交互式聊天模式
- 📝 支持单次问答和批量问答
- 🎭 专业的Coser图片分析功能
- 🌍 中文优化，提供准确的中文回答
- ⚙️ 双模型架构，针对不同任务优化
- 🚫 无干扰模式，隐藏技术错误提示
- 🔄 智能备用机制，确保稳定运行

## ✅ 项目状态

- [x] 基础问答助手 (qa_assistant.py)
- [x] CrewAI功能版本 (qa_assistant_with_crewai.py)
- [x] 图片分析功能 (GLM-4V模型)
- [x] 综合分析功能 (GLM-Z1-Air模型)
- [x] 用户友好界面
- [x] 错误处理和日志记录
- [x] **CrewAI Flows工作流** (coser_analysis_flow.py) ⭐ **推荐使用**

## 环境要求

- Python 3.11+
- 智谱AI API密钥

## 模型配置

系统采用双模型架构，针对不同任务使用最适合的模型：

- **文字问答**: GLM-Z1-Air - 快速响应，适合日常问答
- **图片分析**: GLM-4V - 强大的视觉理解能力，专业图片分析

## 安装依赖

```bash
pip install crewai langchain langchain-openai python-dotenv
```

## 配置

1. 在项目根目录创建 `.env` 文件
2. 添加您的智谱AI API密钥：

```env
ZHIPUAI_API_KEY=your_zhipuai_api_key_here
```

## 版本选择

项目提供三个版本：

1. **coser_analysis_flow.py** (最新推荐)
   - 使用CrewAI Flows工作流
   - 两步骤分析：图片分析 → 综合分析
   - 支持角色信息和文生图prompt生成
   - 专业的工作流管理

2. **qa_assistant.py** (基础推荐)
   - 无干扰版本，直接使用GLM模型
   - 没有技术错误提示，用户体验更好
   - 响应速度更快，更稳定

3. **qa_assistant_with_crewai.py**
   - 保留CrewAI框架功能
   - 支持更复杂的任务处理
   - 隐藏了错误提示但保留CrewAI能力

## 使用方法

### 1. CrewAI Flows工作流（最新推荐）

#### 用户界面模式
```bash
python coser_flow_interface.py
```

提供友好的用户界面，支持：
- 自定义图片分析
- 默认测试图片分析
- 结果保存到文件

#### 快速测试
```bash
python test_flow.py
```

#### 编程调用
```python
from coser_analysis_flow import run_coser_analysis

# 运行完整的工作流
result = run_coser_analysis(
    image_path="图片路径",
    character_name="角色名称",
    additional_description="补充描述"
)

# 获取结果
image_analysis = result["image_analysis"]
comprehensive_analysis = result["comprehensive_analysis"]
```

### 2. 基础交互式模式

运行主程序启动交互式聊天：

```bash
python qa_assistant.py
```

在交互模式下：
- **文字问答**: 直接输入问题
- **图片分析**: 输入 `image:图片路径` 分析Coser图片
- **退出程序**: 输入 `quit` 或 `exit`

或使用CrewAI版本：

```bash
python qa_assistant_with_crewai.py
```

### 3. 编程方式使用

**文字问答**:
```python
from qa_assistant import GLMQAAssistant

# 创建助手实例
assistant = GLMQAAssistant()

# 单次问答
question = "什么是人工智能？"
answer = assistant.ask_question(question)
print(answer)
```

**图片分析**:
```python
from qa_assistant import GLMQAAssistant

# 创建助手实例
assistant = GLMQAAssistant()

# 分析Coser图片
image_path = "coser_photo.jpg"
analysis = assistant.analyze_coser_image(image_path)
print(analysis)
```

### 4. 快速测试

运行测试脚本验证功能：

```bash
# 测试CrewAI Flows工作流
python test_flow.py

# 测试基础功能
python test_image_analysis.py
python image_analysis_example.py
```

## Coser图片分析功能

### CrewAI Flows工作流

新版本采用两步骤工作流：

**步骤一：图片分析Agent**
- 使用GLM-4V模型分析Coser图片
- 六个专业维度的详细分析

**步骤二：综合分析Agent**
- 使用GLM-Z1-Air模型进行综合分析
- 整合图片信息、角色名称和补充描述
- 生成角色详细信息和文生图prompt

### 分析维度

系统会从以下六个专业维度分析Coser图片：

1. **动作姿态**: 识别coser的具体动作（如"跳舞"、"战斗姿势"、"胜利手势"、"微笑挥手"等）
2. **武器道具**: 识别手持或佩戴的道具（如"拿着剑"、"手持法杖"、"背着弓箭"、"戴着头饰"等）
3. **服饰细节**: 分析服装特征（如"穿着校服"、"战斗装"、"和服"、"哥特洛丽塔"等）
4. **发型发色**: 识别coser的发型和发色特征（如"长发"、"短发"、"金色长发"、"黄色长发"等）
5. **妆容分析**: 观察化妆风格（如"淡妆"、"华丽妆容"、"舞台妆"、"特效妆"等）
6. **光线氛围**: 分析打光效果（如"柔光"、"戏剧性光影"、"彩色灯光"等）

### 使用示例

**交互式使用**:
```
💬 请输入问题或图片路径: image:my_coser_photo.jpg
🖼️  正在分析图片: my_coser_photo.jpg
📊 图片分析结果:
[详细的六维度分析结果]
```

**支持的图片格式**: jpg, jpeg, png, bmp等常见格式

## 项目结构

```
.
├── coser_analysis_flow.py       # CrewAI Flows工作流（最新）
├── coser_flow_interface.py      # 用户友好界面
├── test_flow.py                 # 工作流测试脚本
├── qa_assistant.py              # 基础问答助手类
├── qa_assistant_with_crewai.py  # CrewAI功能版本
├── system-prompts/              # 系统提示文件目录
│   └── comprehensive-analysis-prompt.md
├── static/                      # 测试图片目录
│   └── WechatIMG8_640xNaN.jpg
├── README.md                    # 项目说明
├── 项目总结.md                  # 项目总结报告
└── .env                        # 环境变量配置（需要自己创建）
```

## 示例问题

系统可以回答各种类型的问题：

### 通用知识
- "什么是人工智能？"
- "如何学习编程？"
- "解释一下区块链技术"

### 技术问题
- "什么是RESTful API？"
- "Docker和虚拟机有什么区别？"
- "解释微服务架构的优缺点"

## 注意事项

1. 确保网络连接正常，能够访问智谱AI API
2. API密钥需要有足够的调用额度
3. 首次运行可能需要下载模型，请耐心等待
4. 建议在稳定的网络环境下使用

---

**享受与AI助手的对话吧！** 🚀