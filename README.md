# CrawAI 问答助手

基于GLM-4和LangChain的智能对话系统，提供友好的命令行交互界面。

## 功能特性

- 🤖 基于GLM-4大语言模型
- 💬 支持连续对话和上下文记忆
- 📝 对话历史管理和导出
- 🎯 简洁易用的命令行界面
- ⚙️ 灵活的配置管理
- 🌏 中文优化

## 环境要求

- Python 3.11+
- 智谱AI API密钥

## 安装和配置

### 1. 创建虚拟环境

```bash
# 使用pyenv创建虚拟环境
pyenv virtualenv 3.11.11 crawai_3.11.11
pyenv local crawai_3.11.11
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置环境变量

确保`.env`文件中包含您的智谱AI API密钥：

```
ZHIPUAI_API_KEY=your_api_key_here
```

## 使用方法

### 启动问答助手

```bash
python main.py
```

### 可用命令

- `help` - 显示帮助信息
- `clear` - 清除对话历史
- `history` - 显示对话历史
- `info` - 显示会话信息
- `export` - 导出对话历史到JSON文件
- `quit` 或 `exit` - 退出程序

### 使用示例

```
👤 您: 你好，请介绍一下自己
🤖 CrawAI: 您好！我是CrawAI，一个基于GLM-4的智能问答助手...

👤 您: help
可用命令：
  help          - 显示此帮助信息
  clear         - 清除对话历史
  ...

👤 您: info
📊 会话信息：
  模型: glm-4
  温度参数: 0.95
  ...
```

## 项目结构

```
crawai/
├── __init__.py          # 包初始化
├── config.py            # 配置管理
├── llm.py              # GLM模型封装
├── assistant.py        # 问答助手核心
└── cli.py              # 命令行界面
main.py                 # 主程序入口
requirements.txt        # 依赖列表
.env                    # 环境变量配置
README.md              # 项目说明
```

## 配置说明

可以在`crawai/config.py`中修改以下配置：

- `TEMPERATURE`: 模型温度参数 (默认: 0.95)
- `MAX_TOKENS`: 最大输出长度 (默认: 2048)
- `GLM_MODEL`: 使用的模型名称 (默认: "glm-4")

## 开发指南

### 扩展功能

1. 在`crawai/assistant.py`中添加新的助手功能
2. 在`crawai/cli.py`中添加对应的命令行命令
3. 更新帮助信息和文档

### 自定义模型

可以通过修改`crawai/llm.py`中的`GLMChat`类来支持其他模型或调整模型参数。

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
