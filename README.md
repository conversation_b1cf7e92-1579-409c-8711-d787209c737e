# GLM CrewAI 问答助手

基于智谱AI的GLM模型和CrewAI框架构建的智能问答系统。

## 功能特点

- 🤖 使用智谱AI GLM-4模型提供智能回答
- 🔧 基于CrewAI框架构建，支持复杂任务处理
- 💬 支持交互式聊天模式
- 📝 支持单次问答和批量问答
- 🌍 中文优化，提供准确的中文回答
- ⚙️ 可配置的模型参数

## 环境要求

- Python 3.11+
- 智谱AI API密钥

## 安装依赖

```bash
pip install crewai langchain langchain-openai python-dotenv
```

## 配置

1. 在项目根目录创建 `.env` 文件
2. 添加您的智谱AI API密钥：

```env
ZHIPUAI_API_KEY=your_zhipuai_api_key_here
```

## 使用方法

### 1. 交互式问答模式

运行主程序启动交互式聊天：

```bash
python qa_assistant.py
```

### 2. 编程方式使用

```python
from qa_assistant import GLMQAAssistant

# 创建助手实例
assistant = GLMQAAssistant()

# 单次问答
question = "什么是人工智能？"
answer = assistant.ask_question(question)
print(answer)
```

### 3. 运行示例

查看更多使用示例：

```bash
python example_usage.py
```

## 项目结构

```
.
├── qa_assistant.py      # 主要的问答助手类
├── example_usage.py     # 使用示例
├── README.md           # 项目说明
└── .env               # 环境变量配置（需要自己创建）
```

## 示例问题

系统可以回答各种类型的问题：

### 通用知识
- "什么是人工智能？"
- "如何学习编程？"
- "解释一下区块链技术"

### 技术问题
- "什么是RESTful API？"
- "Docker和虚拟机有什么区别？"
- "解释微服务架构的优缺点"

## 注意事项

1. 确保网络连接正常，能够访问智谱AI API
2. API密钥需要有足够的调用额度
3. 首次运行可能需要下载模型，请耐心等待
4. 建议在稳定的网络环境下使用

---

**享受与AI助手的对话吧！** 🚀