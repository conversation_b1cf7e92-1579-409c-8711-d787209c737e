// 全局变量
let uploadedFilePath = null;
let analysisResult = null;

// DOM元素
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const imagePreview = document.getElementById('imagePreview');
const previewImg = document.getElementById('previewImg');
const characterNameInput = document.getElementById('characterName');
const additionalDescriptionInput = document.getElementById('additionalDescription');
const analyzeBtn = document.getElementById('analyzeBtn');
const loadingSection = document.getElementById('loadingSection');
const resultsSection = document.getElementById('resultsSection');

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
});

/**
 * 初始化事件监听器
 */
function initializeEventListeners() {
    // 文件输入变化
    fileInput.addEventListener('change', handleFileSelect);
    
    // 拖拽上传
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    uploadArea.addEventListener('click', () => fileInput.click());
    
    // 表单输入变化
    characterNameInput.addEventListener('input', validateForm);
    additionalDescriptionInput.addEventListener('input', validateForm);
}

/**
 * 处理文件选择
 */
function handleFileSelect(event) {
    console.log('文件选择事件触发');
    const file = event.target.files[0];
    if (file) {
        console.log('选择的文件:', file.name, file.size, file.type);
        uploadFile(file);
    } else {
        console.log('没有选择文件');
    }
}

/**
 * 处理拖拽悬停
 */
function handleDragOver(event) {
    event.preventDefault();
    uploadArea.classList.add('dragover');
}

/**
 * 处理拖拽离开
 */
function handleDragLeave(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
}

/**
 * 处理文件拖拽放下
 */
function handleDrop(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        uploadFile(files[0]);
    }
}

/**
 * 上传文件
 */
async function uploadFile(file) {
    console.log('开始上传文件:', file.name);

    // 验证文件类型
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        console.error('不支持的文件类型:', file.type);
        showToast('不支持的文件格式，请选择图片文件', 'error');
        resetFileInput();
        return;
    }

    // 验证文件大小 (16MB)
    if (file.size > 16 * 1024 * 1024) {
        console.error('文件太大:', file.size);
        showToast('文件大小超过限制（最大16MB）', 'error');
        resetFileInput();
        return;
    }
    
    const formData = new FormData();
    formData.append('file', file);
    
    try {
        showToast('正在上传文件...', 'info');
        
        const response = await fetch('/upload', {
            method: 'POST',
            body: formData
        });

        console.log('上传响应状态:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status}`);
        }

        const result = await response.json();
        console.log('上传响应数据:', result);

        if (result.success) {
            console.log('上传成功，文件路径:', result.filepath);
            uploadedFilePath = result.filepath;
            showImagePreview(file);
            showToast('文件上传成功', 'success');
            validateForm();
        } else {
            console.error('上传失败:', result.error);
            showToast(result.error || '上传失败', 'error');
            resetFileInput();
        }
    } catch (error) {
        console.error('上传错误:', error);
        showToast('上传失败: ' + error.message, 'error');
        resetFileInput();
    }
}

/**
 * 显示图片预览
 */
function showImagePreview(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        previewImg.src = e.target.result;
        uploadArea.style.display = 'none';
        imagePreview.style.display = 'block';
    };
    reader.readAsDataURL(file);
}

/**
 * 重置文件输入
 */
function resetFileInput() {
    console.log('重置文件输入');
    fileInput.value = '';
}

/**
 * 移除图片
 */
function removeImage() {
    console.log('移除图片');
    uploadedFilePath = null;
    uploadArea.style.display = 'block';
    imagePreview.style.display = 'none';
    resetFileInput();
    validateForm();
}

/**
 * 验证表单
 */
function validateForm() {
    const hasFile = uploadedFilePath !== null;
    const hasCharacterName = characterNameInput.value.trim() !== '';
    
    analyzeBtn.disabled = !(hasFile && hasCharacterName);
}

/**
 * 开始分析
 */
async function startAnalysis() {
    if (!uploadedFilePath || !characterNameInput.value.trim()) {
        showToast('请上传图片并输入角色名称', 'error');
        return;
    }
    
    // 隐藏结果区域，显示加载状态
    resultsSection.style.display = 'none';
    loadingSection.style.display = 'block';
    
    // 重置进度步骤
    resetProgressSteps();
    
    try {
        // 步骤1：开始分析
        setActiveStep(1);
        updateLoadingText('正在进行图片分析...');
        
        const requestData = {
            filepath: uploadedFilePath,
            character_name: characterNameInput.value.trim(),
            additional_description: additionalDescriptionInput.value.trim()
        };
        
        const response = await fetch('/analyze', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        
        // 步骤2：处理响应
        setActiveStep(2);
        updateLoadingText('正在生成综合分析...');
        
        const result = await response.json();
        
        if (result.success) {
            // 步骤3：显示结果
            setActiveStep(3);
            updateLoadingText('分析完成！');
            
            setTimeout(() => {
                analysisResult = result.result;
                showResults(result.result);
            }, 1000);
        } else {
            throw new Error(result.error || '分析失败');
        }
        
    } catch (error) {
        loadingSection.style.display = 'none';
        showToast('分析失败: ' + error.message, 'error');
    }
}

/**
 * 重置进度步骤
 */
function resetProgressSteps() {
    document.querySelectorAll('.step').forEach(step => {
        step.classList.remove('active');
    });
}

/**
 * 设置当前活动步骤
 */
function setActiveStep(stepNumber) {
    document.getElementById(`step${stepNumber}`).classList.add('active');
}

/**
 * 更新加载文本
 */
function updateLoadingText(text) {
    document.getElementById('loadingText').textContent = text;
}

/**
 * 显示分析结果
 */
function showResults(result) {
    // 隐藏加载状态，显示结果
    loadingSection.style.display = 'none';
    resultsSection.style.display = 'block';
    
    // 填充结果内容
    document.getElementById('imageAnalysisResult').textContent = result.image_analysis;
    document.getElementById('comprehensiveAnalysisResult').textContent = result.comprehensive_analysis;
    
    // 滚动到结果区域
    resultsSection.scrollIntoView({ behavior: 'smooth' });
    
    showToast('分析完成！', 'success');
}

/**
 * 下载结果
 */
async function downloadResult() {
    if (!analysisResult) {
        showToast('没有可下载的结果', 'error');
        return;
    }
    
    try {
        const response = await fetch('/download_result', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ result: analysisResult })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // 创建下载链接
            const link = document.createElement('a');
            link.href = result.download_url;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            showToast('开始下载结果文件', 'success');
        } else {
            showToast(result.error || '下载失败', 'error');
        }
    } catch (error) {
        showToast('下载失败: ' + error.message, 'error');
    }
}

/**
 * 新的分析
 */
function newAnalysis() {
    // 重置所有状态
    uploadedFilePath = null;
    analysisResult = null;
    
    // 重置界面
    removeImage();
    characterNameInput.value = '';
    additionalDescriptionInput.value = '';
    resultsSection.style.display = 'none';
    loadingSection.style.display = 'none';
    
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
    
    showToast('已重置，可以开始新的分析', 'info');
}

/**
 * 显示消息提示
 */
function showToast(message, type = 'info') {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.className = `toast ${type}`;
    toast.classList.add('show');
    
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}
