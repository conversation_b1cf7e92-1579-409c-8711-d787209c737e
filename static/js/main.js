// 全局变量
let uploadedFilePath = null;
let analysisResult = null;
let isUploading = false; // 防止重复上传的标志
let lastClickTime = 0; // 上次点击时间戳
let fileDialogOpen = false; // 文件对话框是否打开

// DOM元素
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const imagePreview = document.getElementById('imagePreview');
const previewImg = document.getElementById('previewImg');
const characterNameInput = document.getElementById('characterName');
const additionalDescriptionInput = document.getElementById('additionalDescription');
const analyzeBtn = document.getElementById('analyzeBtn');
const loadingSection = document.getElementById('loadingSection');
const resultsSection = document.getElementById('resultsSection');

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
});

/**
 * 初始化事件监听器
 */
function initializeEventListeners() {
    console.log('初始化事件监听器');

    // 移除可能存在的旧监听器（防止重复绑定）
    fileInput.removeEventListener('change', handleFileSelect);
    uploadArea.removeEventListener('click', handleUploadAreaClick);

    // 文件输入变化
    fileInput.addEventListener('change', handleFileSelect);

    // 拖拽上传
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    uploadArea.addEventListener('click', handleUploadAreaClick);

    // 表单输入变化
    characterNameInput.addEventListener('input', validateForm);
    additionalDescriptionInput.addEventListener('input', validateForm);

    console.log('事件监听器初始化完成');
}

/**
 * 处理上传区域点击
 */
function handleUploadAreaClick(event) {
    const currentTime = Date.now();
    console.log('上传区域点击事件触发, 时间:', currentTime);

    // 防抖检查 - 500ms内的重复点击将被忽略
    if (currentTime - lastClickTime < 500) {
        console.log('点击过于频繁，忽略 (间隔:', currentTime - lastClickTime, 'ms)');
        return;
    }

    // 防止重复处理
    if (isUploading || fileDialogOpen) {
        console.log('正在上传中或文件对话框已打开，忽略点击');
        return;
    }

    lastClickTime = currentTime;
    fileDialogOpen = true;

    console.log('打开文件选择对话框');
    fileInput.click();

    // 设置超时重置对话框状态（防止对话框被取消时状态卡住）
    setTimeout(() => {
        if (fileDialogOpen) {
            console.log('重置文件对话框状态（超时）');
            fileDialogOpen = false;
        }
    }, 3000);
}

/**
 * 处理文件选择
 */
function handleFileSelect(event) {
    console.log('文件选择事件触发');

    // 重置对话框状态
    fileDialogOpen = false;

    // 防止重复处理
    if (isUploading) {
        console.log('正在上传中，忽略重复选择');
        return;
    }

    const file = event.target.files[0];
    if (file) {
        console.log('选择的文件:', file.name, file.size, file.type);
        uploadFile(file);
    } else {
        console.log('没有选择文件或取消选择');
    }
}

/**
 * 处理拖拽悬停
 */
function handleDragOver(event) {
    event.preventDefault();
    uploadArea.classList.add('dragover');
}

/**
 * 处理拖拽离开
 */
function handleDragLeave(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
}

/**
 * 处理文件拖拽放下
 */
function handleDrop(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        uploadFile(files[0]);
    }
}

/**
 * 上传文件
 */
async function uploadFile(file) {
    console.log('开始上传文件:', file.name);

    // 设置上传状态
    isUploading = true;

    // 验证文件类型
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        console.error('不支持的文件类型:', file.type);
        showToast('不支持的文件格式，请选择图片文件', 'error');
        safeResetFileInput();
        return;
    }

    // 验证文件大小 (16MB)
    if (file.size > 16 * 1024 * 1024) {
        console.error('文件太大:', file.size);
        showToast('文件大小超过限制（最大16MB）', 'error');
        safeResetFileInput();
        return;
    }
    
    const formData = new FormData();
    formData.append('file', file);
    
    try {
        showToast('正在上传文件...', 'info');
        
        const response = await fetch('/upload', {
            method: 'POST',
            body: formData
        });

        console.log('上传响应状态:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status}`);
        }

        const result = await response.json();
        console.log('上传响应数据:', result);

        if (result.success) {
            console.log('上传成功，文件路径:', result.filepath);
            uploadedFilePath = result.filepath;
            showImagePreview(file);
            showToast('文件上传成功', 'success');
            validateForm();
        } else {
            console.error('上传失败:', result.error);
            showToast(result.error || '上传失败', 'error');
            safeResetFileInput();
        }
    } catch (error) {
        console.error('上传错误:', error);
        showToast('上传失败: ' + error.message, 'error');
        safeResetFileInput();
    } finally {
        // 重置上传状态
        isUploading = false;
        fileDialogOpen = false;
    }
}

/**
 * 显示图片预览
 */
function showImagePreview(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        previewImg.src = e.target.result;
        uploadArea.style.display = 'none';
        imagePreview.style.display = 'block';
    };
    reader.readAsDataURL(file);
}

/**
 * 重置文件输入
 */
function resetFileInput() {
    console.log('重置文件输入');
    fileInput.value = '';
}

/**
 * 安全地重置文件输入（避免重复触发）
 */
function safeResetFileInput() {
    console.log('安全重置文件输入');
    // 延迟重置，避免立即触发事件
    setTimeout(() => {
        fileInput.value = '';
        isUploading = false;
    }, 100);
}

/**
 * 移除图片
 */
function removeImage() {
    console.log('移除图片');
    uploadedFilePath = null;
    uploadArea.style.display = 'block';
    imagePreview.style.display = 'none';
    resetFileInput();
    validateForm();
}

/**
 * 验证表单
 */
function validateForm() {
    const hasFile = uploadedFilePath !== null;
    const hasCharacterName = characterNameInput.value.trim() !== '';
    
    analyzeBtn.disabled = !(hasFile && hasCharacterName);
}

/**
 * 开始分析
 */
async function startAnalysis() {
    if (!uploadedFilePath || !characterNameInput.value.trim()) {
        showToast('请上传图片并输入角色名称', 'error');
        return;
    }

    // 隐藏结果区域，显示加载状态
    resultsSection.style.display = 'none';
    loadingSection.style.display = 'block';

    // 重置进度步骤
    resetProgressSteps();

    try {
        // 启动分析任务
        const requestData = {
            filepath: uploadedFilePath,
            character_name: characterNameInput.value.trim(),
            additional_description: additionalDescriptionInput.value.trim()
        };

        const response = await fetch('/analyze', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        const result = await response.json();

        if (result.success && result.task_id) {
            // 开始轮询进度
            pollProgress(result.task_id);
        } else {
            throw new Error(result.error || '启动分析失败');
        }

    } catch (error) {
        loadingSection.style.display = 'none';
        showToast('分析失败: ' + error.message, 'error');
    }
}

/**
 * 轮询分析进度
 */
async function pollProgress(taskId) {
    console.log('开始轮询进度，任务ID:', taskId);

    const pollInterval = setInterval(async () => {
        try {
            const response = await fetch(`/progress/${taskId}`);
            const progressData = await response.json();

            console.log('进度更新:', progressData);

            if (progressData.error) {
                clearInterval(pollInterval);
                loadingSection.style.display = 'none';
                showToast('获取进度失败: ' + progressData.error, 'error');
                return;
            }

            // 更新进度显示
            updateProgress(progressData);

            // 检查是否完成
            if (progressData.status === 'completed') {
                clearInterval(pollInterval);
                if (progressData.result) {
                    analysisResult = progressData.result;
                    setTimeout(() => {
                        showResults(progressData.result);
                    }, 1000);
                } else {
                    loadingSection.style.display = 'none';
                    showToast('分析完成但未获取到结果', 'error');
                }
            } else if (progressData.status === 'error') {
                clearInterval(pollInterval);
                loadingSection.style.display = 'none';
                showToast('分析失败: ' + (progressData.error || '未知错误'), 'error');
            }

        } catch (error) {
            console.error('轮询进度错误:', error);
            clearInterval(pollInterval);
            loadingSection.style.display = 'none';
            showToast('获取进度失败: ' + error.message, 'error');
        }
    }, 1000); // 每秒轮询一次
}

/**
 * 更新进度显示
 */
function updateProgress(progressData) {
    // 更新步骤状态
    if (progressData.step) {
        setActiveStep(progressData.step);
    }

    // 更新加载文本
    if (progressData.step_name) {
        updateLoadingText(progressData.step_name);
    }

    console.log(`进度: ${progressData.progress || 0}% - ${progressData.step_name || '处理中...'}`);
}

/**
 * 重置进度步骤
 */
function resetProgressSteps() {
    document.querySelectorAll('.step').forEach(step => {
        step.classList.remove('active');
    });
}

/**
 * 设置当前活动步骤
 */
function setActiveStep(stepNumber) {
    document.getElementById(`step${stepNumber}`).classList.add('active');
}

/**
 * 更新加载文本
 */
function updateLoadingText(text) {
    document.getElementById('loadingText').textContent = text;
}

/**
 * 显示分析结果
 */
function showResults(result) {
    // 隐藏加载状态，显示结果
    loadingSection.style.display = 'none';
    resultsSection.style.display = 'block';
    
    // 填充结果内容
    document.getElementById('imageAnalysisResult').textContent = result.image_analysis;
    document.getElementById('comprehensiveAnalysisResult').textContent = result.comprehensive_analysis;
    
    // 滚动到结果区域
    resultsSection.scrollIntoView({ behavior: 'smooth' });
    
    showToast('分析完成！', 'success');
}

/**
 * 下载结果
 */
async function downloadResult() {
    if (!analysisResult) {
        showToast('没有可下载的结果', 'error');
        return;
    }
    
    try {
        const response = await fetch('/download_result', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ result: analysisResult })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // 创建下载链接
            const link = document.createElement('a');
            link.href = result.download_url;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            showToast('开始下载结果文件', 'success');
        } else {
            showToast(result.error || '下载失败', 'error');
        }
    } catch (error) {
        showToast('下载失败: ' + error.message, 'error');
    }
}

/**
 * 新的分析
 */
function newAnalysis() {
    // 重置所有状态
    uploadedFilePath = null;
    analysisResult = null;
    
    // 重置界面
    removeImage();
    characterNameInput.value = '';
    additionalDescriptionInput.value = '';
    resultsSection.style.display = 'none';
    loadingSection.style.display = 'none';
    
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
    
    showToast('已重置，可以开始新的分析', 'info');
}

/**
 * 显示消息提示
 */
function showToast(message, type = 'info') {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.className = `toast ${type}`;
    toast.classList.add('show');
    
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}
