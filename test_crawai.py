#!/usr/bin/env python3
"""
CrawAI 测试脚本
用于验证核心功能是否正常工作
"""

import sys
import traceback
from crawai.config import Config
from crawai.assistant import CrawAIAssistant


def test_config():
    """测试配置模块"""
    print("🔧 测试配置模块...")
    try:
        Config.validate()
        print(f"✅ API密钥已配置: {Config.ZHIPUAI_API_KEY[:10]}...")
        print(f"✅ 模型: {Config.GLM_MODEL}")
        print(f"✅ 温度: {Config.TEMPERATURE}")
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def test_assistant():
    """测试问答助手"""
    print("\n🤖 测试问答助手...")
    try:
        assistant = CrawAIAssistant()
        print("✅ 助手初始化成功")
        
        # 测试简单问答
        print("📝 测试简单问答...")
        result = assistant.ask("你好")
        print(f"✅ 问答测试成功，回答长度: {len(result['answer'])} 字符")
        print(f"   回答预览: {result['answer'][:50]}...")
        
        # 测试会话信息
        print("📊 测试会话信息...")
        info = assistant.get_session_info()
        print(f"✅ 会话信息获取成功，已提问 {info['questions_asked']} 次")
        
        return True
    except Exception as e:
        print(f"❌ 助手测试失败: {e}")
        traceback.print_exc()
        return False


def test_conversation_flow():
    """测试对话流程"""
    print("\n💬 测试对话流程...")
    try:
        assistant = CrawAIAssistant()
        
        # 多轮对话测试
        questions = [
            "你好，请介绍一下自己",
            "你能做什么？",
            "谢谢你的介绍"
        ]
        
        for i, question in enumerate(questions, 1):
            print(f"   第{i}轮对话...")
            result = assistant.ask(question)
            if result['answer']:
                print(f"   ✅ 第{i}轮对话成功")
            else:
                print(f"   ❌ 第{i}轮对话失败")
                return False
        
        # 测试对话历史
        history = assistant.get_conversation_history()
        print(f"✅ 对话历史记录: {len(history)} 条")
        
        return True
    except Exception as e:
        print(f"❌ 对话流程测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始CrawAI功能测试\n")
    
    tests = [
        ("配置模块", test_config),
        ("问答助手", test_assistant),
        ("对话流程", test_conversation_flow)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"{'='*50}")
        print(f"测试: {test_name}")
        print(f"{'='*50}")
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过\n")
        else:
            print(f"❌ {test_name} 测试失败\n")
    
    print(f"{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print(f"{'='*50}")
    
    if passed == total:
        print("🎉 所有测试通过！CrawAI已准备就绪。")
        print("\n🚀 运行 'python main.py' 开始使用CrawAI问答助手")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
