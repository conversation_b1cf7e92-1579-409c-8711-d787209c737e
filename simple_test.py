#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试问答助手
"""

from qa_assistant import GLMQAAssistant

def main():
    """简单测试"""
    print("🚀 开始测试GLM CrewAI问答助手...")
    
    try:
        # 创建助手实例
        assistant = GLMQAAssistant()
        print("✅ 助手初始化成功")
        
        # 测试问题
        question = "什么是Python？请简要介绍。"
        print(f"\n❓ 测试问题: {question}")
        print("-" * 50)
        
        # 获取回答
        answer = assistant.ask_question(question)
        print(f"🤖 回答: {answer}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    main()
