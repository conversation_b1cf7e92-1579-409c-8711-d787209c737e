#!/usr/bin/env python3
"""
CrawAI 演示脚本
展示问答助手的基本功能
"""

from crawai.assistant import CrawAIAssistant


def demo():
    """演示CrawAI功能"""
    print("🎯 CrawAI 问答助手演示")
    print("=" * 50)
    
    # 初始化助手
    assistant = CrawAIAssistant()
    
    # 演示问题列表
    demo_questions = [
        "你好，请介绍一下自己",
        "你能帮我解决什么问题？",
        "请解释一下什么是人工智能",
        "谢谢你的回答"
    ]
    
    # 逐个提问并显示回答
    for i, question in enumerate(demo_questions, 1):
        print(f"\n[{i}] 👤 用户: {question}")
        print("🤔 思考中...")
        
        result = assistant.ask(question)
        print(f"🤖 CrawAI: {result['answer']}")
        print("-" * 50)
    
    # 显示会话信息
    print("\n📊 会话信息:")
    info = assistant.get_session_info()
    print(f"  • 模型: {info['model']}")
    print(f"  • 提问次数: {info['questions_asked']}")
    print(f"  • 会话时长: {info['session_duration_seconds']:.1f} 秒")
    
    # 显示对话历史
    print("\n📝 对话历史:")
    history = assistant.get_conversation_history()
    for i, item in enumerate(history, 1):
        print(f"  {i}. Q: {item['question'][:30]}...")
        print(f"     A: {item['answer'][:50]}...")
    
    print("\n✨ 演示完成！运行 'python main.py' 开始交互式对话。")


if __name__ == "__main__":
    demo()
