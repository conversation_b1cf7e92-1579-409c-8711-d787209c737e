#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GLM CrewAI 问答助手演示程序
展示问答助手的各种功能
"""

from qa_assistant import GLMQAAssistant
import time

def print_separator(title=""):
    """打印分隔线"""
    if title:
        print(f"\n{'='*20} {title} {'='*20}")
    else:
        print("="*60)

def demo_single_question():
    """演示单次问答"""
    print_separator("单次问答演示")

    try:
        assistant = GLMQAAssistant()

        question = "CrewAI是什么？它有什么特点？"
        print(f"🤔 问题: {question}")
        print("-" * 50)

        answer = assistant.ask_question(question)
        print(f"🤖 回答:\n{answer}")

    except Exception as e:
        print(f"❌ 错误: {str(e)}")

def main():
    """主演示程序"""
    print("🚀 GLM CrewAI 问答助手 - 功能演示")
    print("本程序将展示问答助手的基本功能")
    print_separator()

    # 演示单次问答
    demo_single_question()

    print_separator("演示完成")
    print("✅ 演示完成！")
    print("\n💡 使用提示:")
    print("- 运行 'python qa_assistant.py' 启动交互式问答")
    print("- 运行 'python simple_test.py' 进行简单测试")
    print("- 查看 'README.md' 了解更多使用方法")
    print_separator()

if __name__ == "__main__":
    main()