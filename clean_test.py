#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清洁版本测试 - 验证无错误提示的问答助手
"""

from qa_assistant import GLMQAAssistant

def main():
    """测试清洁版本的问答助手"""
    print("🧹 测试清洁版本问答助手（无错误提示）")
    print("=" * 60)
    
    try:
        # 创建助手实例
        assistant = GLMQAAssistant()
        print("✅ 助手初始化成功")
        
        # 测试问题列表
        questions = [
            "什么是机器学习？",
            "Python有哪些优势？",
            "如何学习编程？"
        ]
        
        for i, question in enumerate(questions, 1):
            print(f"\n📝 问题 {i}: {question}")
            print("-" * 50)
            
            # 获取回答
            answer = assistant.ask_question(question)
            print(f"🤖 回答: {answer}")
            
            if i < len(questions):
                print("\n" + "."*60)
        
        print("\n" + "=" * 60)
        print("✅ 测试完成！注意：应该没有任何错误提示出现")
        print("🎉 用户体验已优化，界面更加清洁")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    main()
