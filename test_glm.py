#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GLM API连接
"""

import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI

# 加载环境变量
load_dotenv()

def test_glm_connection():
    """测试GLM API连接"""
    try:
        # 获取API密钥
        api_key = os.getenv("ZHIPUAI_API_KEY")
        print(f"API Key: {api_key[:10]}...{api_key[-10:] if api_key else 'None'}")
        
        if not api_key:
            print("❌ 错误: 未找到ZHIPUAI_API_KEY环境变量")
            return False
            
        # 创建LLM实例
        llm = ChatOpenAI(
            temperature=0.7,
            model="glm-4",
            openai_api_key=api_key,
            openai_api_base="https://open.bigmodel.cn/api/paas/v4/",
            max_tokens=100
        )
        
        print("🔄 正在测试API连接...")
        
        # 发送简单的测试请求
        response = llm.invoke("你好，请简单回复一下")
        print(f"✅ API连接成功!")
        print(f"回复: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ API连接失败: {str(e)}")
        return False

if __name__ == "__main__":
    test_glm_connection()
